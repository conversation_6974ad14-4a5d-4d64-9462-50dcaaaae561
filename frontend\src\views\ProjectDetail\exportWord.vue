<template>
  <div>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, ImageRun, AlignmentType } from 'docx'
import { saveAs } from 'file-saver'
import dayjs from 'dayjs'
import { paramsList, outputInfo } from './util'

// 接收父组件传递的数据
const props = defineProps({
  detailData: {
    type: Object,
    default: () => ({})
  },
  solutionParams: {
    type: Object,
    default: () => ({})
  }
})

// 导出Word文档
const exportWordDoc = async () => {
  try {
    // 创建文档
    const doc = new Document({
      sections: [
        // 首页
        {
          properties: {},
          children: [
            // 首页标题 - 第一行
            new Paragraph({
              children: [
                new TextRun({
                  text: "ABDEFG研究技术公司项目",
                  size: 32,
                  bold: true
                })
              ],
              alignment: AlignmentType.CENTER,
              spacing: { before: 2000, after: 400 }
            }),
            // 首页标题 - 第二行
            new Paragraph({
              children: [
                new TextRun({
                  text: `${props.solutionParams.projectName || 'XX'} 项目容量测算报告`,
                  size: 28,
                  bold: true
                })
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 2000 }
            }),
            // 分页符
            new Paragraph({
              children: [new TextRun({ text: "", break: 1 })],
              pageBreakBefore: true
            })
          ]
        },
        // 正文内容
        {
          properties: {},
          children: [
            // 一、项目信息
            new Paragraph({
              text: "一、项目信息",
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...generateProjectInfo(),

            // 二、项目配置
            new Paragraph({
              text: "二、项目配置",
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...generateProjectConfig(),

            // 三、运行结果
            new Paragraph({
              text: "三、运行结果",
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...generateRunResults(),

            // 四、计算逻辑
            new Paragraph({
              text: "四、计算逻辑",
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...await generateCalculationLogic()
          ]
        }
      ]
    })

    // 生成并下载文档
    const blob = await Packer.toBlob(doc)
    const fileName = `${props.detailData.projectName || '项目'}_容量测算报告_${dayjs().format('YYYY-MM-DD')}.docx`
    saveAs(blob, fileName)

    message.success('Word文档导出成功')
  } catch (error) {
    console.error('导出Word文档失败:', error)
    message.error('导出Word文档失败')
  }
}

// 生成项目信息内容
const generateProjectInfo = () => {
  const content = []
  const projectInfoParams = [
    { label: '项目名称', name: 'projectName', unit: '' },
    { label: '客户名称', name: 'customer', unit: '' },
    { label: '项目周期', name: 'cycle', unit: '年' },
    { label: '年产氢量', name: 'h2Product', unit: '吨' },
    { label: '项目描述', name: 'projectDesc', unit: '' },
  ]

  projectInfoParams.forEach(param => {
    const value = props.solutionParams[param.name]
    const displayValue = value !== undefined && value !== null ? value.toString() : '—'

    content.push(new Paragraph({
      children: [
        new TextRun({ text: `${param.label}${param.unit ? `(${param.unit})` : ''}：`, bold: true }),
        new TextRun(displayValue)
      ],
      spacing: { after: 100 }
    }))
  })

  return content
}

// 生成项目配置内容
const generateProjectConfig = () => {
  const content = []
  // 过滤掉项目信息，只保留当前拓扑存在的配置项
  const visibleParams = paramsList(props.solutionParams.topology).filter(item => item.visible && item.title !== '项目信息')

  let sectionIndex = 1
  visibleParams.forEach(item => {
    // 添加二级标题，序号递增（如2.1、2.2等）
    content.push(new Paragraph({
      text: `2.${sectionIndex} ${item.title}配置`,
      heading: HeadingLevel.HEADING_2,
      spacing: { before: 300, after: 150 }
    }))

    // 添加参数列表
    item.params.forEach(param => {
      const value = props.solutionParams[param.name]
      const displayValue = value !== undefined && value !== null ?
        (param.unit === '%' ? (value * 100).toFixed(2) : value) : '—'

      content.push(new Paragraph({
        children: [
          new TextRun({ text: `${param.label}${param.unit ? `(${param.unit})` : ''}：`, bold: true }),
          new TextRun(displayValue.toString())
        ],
        spacing: { after: 100 }
      }))
    })

    sectionIndex++
  })

  return content
}

// 生成运行结果内容
const generateRunResults = () => {
  const content = []
  const resultSections = outputInfo()

  let sectionIndex = 1
  resultSections.forEach(section => {
    // 添加二级标题，序号递增（如3.1、3.2等）
    content.push(new Paragraph({
      text: `3.${sectionIndex} ${section.mainTitle}`,
      heading: HeadingLevel.HEADING_2,
      spacing: { before: 300, after: 150 }
    }))

    // 如果有list，添加参数列表
    if (section.list) {
      section.list.forEach(item => {
        const value = props.detailData[item.name]
        let displayValue = '—'

        if (value !== undefined && value !== null) {
          if (item.numberType === 'ratio') {
            displayValue = (value * 100).toFixed(2)
          } else {
            displayValue = value.toString()
          }
        }

        content.push(new Paragraph({
          children: [
            new TextRun({ text: `${item.label}${item.unit ? `(${item.unit})` : ''}：`, bold: true }),
            new TextRun(displayValue)
          ],
          spacing: { after: 100 }
        }))
      })
    }

    // 如果是设备相关的section（有key属性）
    if (section.key && props.detailData[section.key]) {
      const deviceData = props.detailData[section.key]
      if (Array.isArray(deviceData)) {
        deviceData.forEach((device, index) => {
          content.push(new Paragraph({
            text: `设备 ${index + 1}`,
            heading: HeadingLevel.HEADING_3,
            spacing: { before: 200, after: 100 }
          }))

          // 添加设备参数
          Object.entries(device).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              content.push(new Paragraph({
                children: [
                  new TextRun({ text: `${key}：`, bold: true }),
                  new TextRun(value.toString())
                ],
                spacing: { after: 80 }
              }))
            }
          })
        })
      }
    }

    sectionIndex++
  })

  return content
}

// 生成计算逻辑内容
const generateCalculationLogic = async () => {
  const content = []

  // 4.1 轮动运行策略
  content.push(new Paragraph({
    text: "4.1 轮动运行策略",
    heading: HeadingLevel.HEADING_2,
    spacing: { before: 300, after: 150 }
  }))

  content.push(new Paragraph({
    children: [
      new TextRun({
        text: "  通过动态调度多台电解槽的启停、负荷调整，平衡生产需求与系统稳定性，常见于电解水制氢等场景。策略需先明确目标再基于实时数据分配任务：优先启用运行效率高、状态稳定的电解槽承担基础负荷；同时需兼顾槽体寿命与能耗优化，部分场景还会结合储能、电网调度信号，提升整体系统的经济性与可靠性。"
      })
    ],
    spacing: { after: 200 }
  }))

  // 添加图片1
  try {
    const imageBuffer1 = await loadImageAsBuffer('./doc1.png')
    content.push(new Paragraph({
      children: [
        new ImageRun({
          data: imageBuffer1,
          transformation: {
            width: 480,
            height: 360,
          },
        })
      ],
      alignment: AlignmentType.CENTER,
      spacing: { after: 300 }
    }))
  } catch (error) {
    console.warn('无法加载图片 doc1.png:', error)
  }

  // 4.2 停机策略
  content.push(new Paragraph({
    text: "4.2 停机策略",
    heading: HeadingLevel.HEADING_2,
    spacing: { before: 300, after: 150 }
  }))

  content.push(new Paragraph({
    children: [
      new TextRun({
        text: "  电解槽停机策略是为了安全、平稳关停设备，避免损伤部件或影响后续重启，核心是\"分步泄压、降温、断能，兼顾安全与设备保护\"，常见于电解水制氢、金属电解等场景，为下次启动做好准备。"
      })
    ],
    spacing: { after: 200 }
  }))

  // 添加图片2
  try {
    const imageBuffer2 = await loadImageAsBuffer('./doc2.png')
    content.push(new Paragraph({
      children: [
        new ImageRun({
          data: imageBuffer2,
          transformation: {
            width: 480,
            height: 360,
          },
        })
      ],
      alignment: AlignmentType.CENTER,
      spacing: { after: 300 }
    }))
  } catch (error) {
    console.warn('无法加载图片 doc2.png:', error)
  }

  // 4.3 算法策略
  content.push(new Paragraph({
    text: "4.3 算法策略",
    heading: HeadingLevel.HEADING_2,
    spacing: { before: 300, after: 150 }
  }))

  content.push(new Paragraph({
    children: [
      new TextRun({
        text: "  粒子群算法（PSO）可用于优化电解槽的策略。在该应用中，一个\"粒子\"代表一套具体的操作参数组合。算法通过模拟群体迭代，不断评估不同策略的综合成本。粒子根据自身历史最佳和群体历史最佳位置更新搜索方向，最终快速收敛到一个能显著降低启停过程总成本、延长设备寿命且保证安全性的近似最优操作方案。"
      })
    ],
    spacing: { after: 200 }
  }))

  // 添加图片3
  try {
    const imageBuffer3 = await loadImageAsBuffer('./doc3.png')
    content.push(new Paragraph({
      children: [
        new ImageRun({
          data: imageBuffer3,
          transformation: {
            width: 480,
            height: 360,
          },
        })
      ],
      alignment: AlignmentType.CENTER,
      spacing: { after: 300 }
    }))
  } catch (error) {
    console.warn('无法加载图片 doc3.png:', error)
  }

  return content
}

// 加载图片为Buffer
const loadImageAsBuffer = async (imagePath) => {
  try {
    // 构建完整的图片路径
    const fullPath = `${window.location.origin}${imagePath}`
    const response = await fetch(fullPath)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const arrayBuffer = await response.arrayBuffer()
    return new Uint8Array(arrayBuffer)
  } catch (error) {
    console.error('加载图片失败:', error)
    throw error
  }
}

// 暴露导出方法给父组件
defineExpose({
  exportWordDoc
})
</script>

<style scoped>
/* 这个组件不需要样式 */
</style>