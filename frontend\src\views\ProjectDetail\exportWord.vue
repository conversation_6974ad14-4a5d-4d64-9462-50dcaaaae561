<template>
  <div>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, ImageRun, AlignmentType, Table, TableRow, TableCell, WidthType } from 'docx'
import { saveAs } from 'file-saver'
import dayjs from 'dayjs'
import { paramsList, outputInfo } from './util'

// 第四部分计算逻辑的数据
const calculationLogicData = [
  {
    title: "4.1 轮动运行策略",
    content: "  通过动态调度多台电解槽的启停、负荷调整，平衡生产需求与系统稳定性，常见于电解水制氢等场景。策略需先明确目标再基于实时数据分配任务：优先启用运行效率高、状态稳定的电解槽承担基础负荷；同时需兼顾槽体寿命与能耗优化，部分场景还会结合储能、电网调度信号，提升整体系统的经济性与可靠性。",
    img: "/doc1.png"
  },
  {
    title: "4.2 停机策略",
    content: "  电解槽停机策略是为了安全、平稳关停设备，避免损伤部件或影响后续重启，核心是\"分步泄压、降温、断能，兼顾安全与设备保护\"，常见于电解水制氢、金属电解等场景，为下次启动做好准备。",
    img: "/doc2.png"
  },
  {
    title: "4.3 算法策略",
    content: "  粒子群算法（PSO）可用于优化电解槽的策略。在该应用中，一个\"粒子\"代表一套具体的操作参数组合。算法通过模拟群体迭代，不断评估不同策略的综合成本。粒子根据自身历史最佳和群体历史最佳位置更新搜索方向，最终快速收敛到一个能显著降低启停过程总成本、延长设备寿命且保证安全性的近似最优操作方案。",
    img: "/doc3.png"
  }
]

// 接收父组件传递的数据
const props = defineProps({
  detailData: {
    type: Object,
    default: () => ({})
  },
  solutionParams: {
    type: Object,
    default: () => ({})
  }
})

// 导出Word文档
const exportWordDoc = async () => {
  try {
    // 先生成所有内容，避免在Document构造函数中使用await
    const projectInfo = generateProjectInfo()
    const projectConfig = generateProjectConfig()
    const runResults = generateRunResults()
    const calculationLogic = await generateCalculationLogic()

    // 创建文档
    const doc = new Document({
      styles: {
        default: {
          heading1: {
            run: {
              color: "000000",
              bold: true,
              size: 24
            }
          },
          heading2: {
            run: {
              color: "000000",
              bold: true,
              size: 20
            }
          }
        }
      },
      sections: [
        {
          properties: {},
          children: [
            // 首页标题 - 第一行
            new Paragraph({
              children: [
                new TextRun({
                  text: "ABDEFG研究技术公司项目",
                  size: 32,
                  bold: true,
                  color: "000000"
                })
              ],
              alignment: AlignmentType.CENTER,
              spacing: { before: 2000, after: 400 }
            }),
            // 首页标题 - 第二行
            new Paragraph({
              children: [
                new TextRun({
                  text: `${props.solutionParams.projectName || 'XX'} 项目容量测算报告`,
                  size: 28,
                  bold: true,
                  color: "000000"
                })
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 2000 }
            }),
            // 分页符
            new Paragraph({
              children: [new TextRun({ text: "" })],
              pageBreakBefore: true
            }),

            // 一、项目信息
            new Paragraph({
              children: [
                new TextRun({
                  text: "一、项目信息",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...projectInfo,

            // 二、项目配置
            new Paragraph({
              children: [
                new TextRun({
                  text: "二、项目配置",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...projectConfig,

            // 三、运行结果
            new Paragraph({
              children: [
                new TextRun({
                  text: "三、运行结果",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...runResults,

            // 四、计算逻辑
            new Paragraph({
              children: [
                new TextRun({
                  text: "四、计算逻辑",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...calculationLogic
          ]
        }
      ]
    })

    // 生成并下载文档
    const blob = await Packer.toBlob(doc)
    const fileName = `${props.detailData.projectName || '项目'}_容量测算报告2_${dayjs().format('YYYY-MM-DD')}.docx`
    saveAs(blob, fileName)

    message.success('Word文档导出成功')
  } catch (error) {
    console.error('导出Word文档失败:', error)
    message.error('导出Word文档失败')
  }
}

// 生成项目信息内容
const generateProjectInfo = () => {
  const content = []
  const projectInfoParams = [
    { label: '项目名称', name: 'projectName', unit: '' },
    { label: '客户名称', name: 'customer', unit: '' },
    { label: '项目周期', name: 'cycle', unit: '年' },
    { label: '年产氢量', name: 'h2Product', unit: '吨' },
    { label: '项目描述', name: 'projectDesc', unit: '' },
  ]

  projectInfoParams.forEach(param => {
    const value = props.solutionParams[param.name]
    const displayValue = value !== undefined && value !== null ? value.toString() : '—'

    content.push(new Paragraph({
      children: [
        new TextRun({
          text: `${param.label}${param.unit ? `(${param.unit})` : ''}：`,
          bold: true
        }),
        new TextRun({
          text: displayValue
        })
      ],
      spacing: { after: 100 }
    }))
  })

  return content
}

// 生成项目配置内容
const generateProjectConfig = () => {
  const content = []
  // 过滤掉项目信息，只保留当前拓扑存在的配置项
  const visibleParams = paramsList(props.solutionParams.topology).filter(item => item.visible && item.title !== '项目信息')

  let sectionIndex = 1
  visibleParams.forEach(item => {
    // 添加二级标题，序号递增（如2.1、2.2等）
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: `2.${sectionIndex} ${item.title}配置`,
          bold: true,
          size: 20,
          color: "000000"
        })
      ],
      heading: HeadingLevel.HEADING_2,
      spacing: { before: 300, after: 150 }
    }))

    // 添加参数列表
    item.params.forEach(param => {
      const value = props.solutionParams[param.name]
      const displayValue = value !== undefined && value !== null ?
        (param.unit === '%' ? (value * 100).toFixed(2) : value) : '—'

      content.push(new Paragraph({
        children: [
          new TextRun({
            text: `${param.label}${param.unit ? `(${param.unit})` : ''}：`,
            bold: true
          }),
          new TextRun({
            text: displayValue.toString()
          })
        ],
        spacing: { after: 100 }
      }))
    })

    sectionIndex++
  })

  return content
}

// 生成运行结果内容
const generateRunResults = () => {
  const content = []
  const resultSections = outputInfo()

  let sectionIndex = 1
  resultSections.forEach(section => {
    // 添加二级标题，序号递增（如3.1、3.2等）
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: `3.${sectionIndex} ${section.mainTitle}`,
          bold: true,
          size: 20,
          color: "000000"
        })
      ],
      heading: HeadingLevel.HEADING_2,
      spacing: { before: 300, after: 150 }
    }))

    // 如果有list，添加参数列表
    if (section.list) {
      section.list.forEach(item => {
        const value = props.detailData[item.name]
        let displayValue = '—'

        if (value !== undefined && value !== null) {
          if (item.numberType === 'ratio') {
            displayValue = (value * 100).toFixed(2)
          } else {
            displayValue = value.toString()
          }
        }

        content.push(new Paragraph({
          children: [
            new TextRun({
              text: `${item.label}${item.unit ? `(${item.unit})` : ''}：`,
              bold: true
            }),
            new TextRun({
              text: displayValue
            })
          ],
          spacing: { after: 100 }
        }))
      })
    }

    // 如果是设备相关的section（有key属性），使用表格显示
    if (section.key === 'alk' && props.detailData.alk) {
      // 电解槽产品表格
      const alkData = props.detailData.alk
      if (Array.isArray(alkData) && alkData.length > 0) {
        const tableRows = []

        // 表头
        tableRows.push(new TableRow({
          children: [
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "型号",
                  bold: true
                })]
              })]
            }),
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "容量(Nm³/h)",
                  bold: true
                })]
              })]
            }),
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "电耗(kwh/Nm³)",
                  bold: true
                })]
              })]
            }),
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "功率(MW)",
                  bold: true
                })]
              })]
            }),
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "数量",
                  bold: true
                })]
              })]
            })
          ]
        }))

        // 数据行
        alkData.forEach(device => {
          tableRows.push(new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: device.name?.toString() || '—'
                  })]
                })]
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: device.capacity?.toString() || '—'
                  })]
                })]
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: device.power_consumption?.toString() || '—'
                  })]
                })]
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: device.pe?.toString() || '—'
                  })]
                })]
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: device.number?.toString() || '—'
                  })]
                })]
              })
            ]
          }))
        })

        content.push(new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE }
        }))
      }
    } else if (section.key === 'alkStorage' && props.detailData.hs_plan) {
      // 储氢统计表格
      const storageData = props.detailData.hs_plan
      if (Array.isArray(storageData) && storageData.length > 0) {
        const tableRows = []

        // 表头
        tableRows.push(new TableRow({
          children: [
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "储罐厂商",
                  bold: true
                })]
              })]
            }),
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "储罐型号",
                  bold: true
                })]
              })]
            }),
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "储罐容量(m³)",
                  bold: true
                })]
              })]
            }),
            new TableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: "储罐数量",
                  bold: true
                })]
              })]
            })
          ]
        }))

        // 数据行
        storageData.forEach(storage => {
          tableRows.push(new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: storage.manufacturer?.toString() || '—'
                  })]
                })]
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: storage.model?.toString() || '—'
                  })]
                })]
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: storage.volume?.toString() || '—'
                  })]
                })]
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: storage.number?.toString() || '—'
                  })]
                })]
              })
            ]
          }))
        })

        content.push(new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE }
        }))
      }
    }

    sectionIndex++
  })

  return content
}

// 生成计算逻辑内容
const generateCalculationLogic = async () => {
  const content = []

  // 动态处理计算逻辑数据
  for (let i = 0; i < calculationLogicData.length; i++) {
    const item = calculationLogicData[i]

    // 添加标题
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: item.title,
          bold: true,
          size: 20,
          color: "000000"
        })
      ],
      heading: HeadingLevel.HEADING_2,
      spacing: { before: 300, after: 150 }
    }))

    // 添加内容段落
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: item.content
        })
      ],
      spacing: { after: 200 }
    }))

    // 添加图片
    if (item.img) {
      try {
        const imageBuffer = await loadImageAsBuffer(item.img)
        content.push(new Paragraph({
          children: [
            new ImageRun({
              data: imageBuffer,
              transformation: {
                width: 480,
                height: 360,
              },
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 300 }
        }))
      } catch (error) {
        console.warn(`无法加载图片 ${item.img}:`, error)
        // 如果图片加载失败，添加一个占位文本
        content.push(new Paragraph({
          children: [
            new TextRun({
              text: `[图片: ${item.img}]`,
              italics: true,
              color: "666666"
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 300 }
        }))
      }
    }
  }

  return content
}

// 加载图片为Buffer
const loadImageAsBuffer = async (imagePath) => {
  try {
    // 构建完整的图片路径，使用localhost地址
    const fullPath = `http://localhost:5173${imagePath}`
    console.log('尝试加载图片:', fullPath)

    const response = await fetch(fullPath)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const arrayBuffer = await response.arrayBuffer()
    return new Uint8Array(arrayBuffer)
  } catch (error) {
    console.error('加载图片失败:', error)
    throw error
  }
}

// 暴露导出方法给父组件
defineExpose({
  exportWordDoc
})
</script>

<style scoped>
/* 这个组件不需要样式 */
</style>